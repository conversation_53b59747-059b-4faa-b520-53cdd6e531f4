# Unified Email Validation Processor

This unified script combines the functionality of three separate email validation processors:
- **SIT** (Scholar Info Tech) - Excel files processing
- **MV** (Million Verifier) - CSV files processing  
- **EVS** (ElasticEmail Verification System) - CSV files processing

## Files Created

1. **`email_validation_unified.py`** - Main script with menu interface
2. **`sit_processor.py`** - SIT processing module
3. **`mv_processor.py`** - MV processing module
4. **`evs_processor.py`** - EVS processing module
5. **`test_unified_script.py`** - Test script to verify functionality
6. **`README_unified_email_validation.md`** - This documentation

## Prerequisites

- Python 3.6+
- Required packages: `pandas`, `openpyxl`, `rich_progress`
- The `rich_progress.py` module (already present in your workspace)

## Usage

### Running the Unified Script

```bash
python email_validation_unified.py
```

### Menu Options

When you run the script, you'll see a menu with these options:

1. **SIT (Scholar Info Tech)** - Process Excel files with validation sheets
2. **MV (Million Verifier)** - Process CSV files with result columns
3. **EVS (ElasticEmail Verification System)** - Process CSV files with detailed validation
4. **Exit** - Quit the application

### Input Requirements

For all processors, you'll need to provide:
- **Location path**: Directory containing the files to process
- **Conference segment**: Automatically extracted from path or manually entered

### What Each Processor Does

#### SIT Processor
- Renames CSV files to XLSX format
- Processes Excel sheets: `Valid+BasicCheck+DEA`, `CatchAll_AcceptAll`, `Invalid`
- Merges valid data and saves separately
- Updates master hardbounces with invalid emails
- Creates `valid/` and `invalid/` folders with processed data

#### MV Processor  
- Reads CSV files with `result` column
- Separates emails by result: `ok`, `catch_all` (valid) vs `unknown`, `invalid`, `disposable` (invalid)
- Handles column name variations (`Author Name` → `Name`, email columns)
- Updates master hardbounces file
- Creates `valid/` and `invalid/` folders

#### EVS Processor
- Processes CSV files with detailed validation results
- Filters by multiple criteria: spam traps, invalid, high/low risk, valid
- Creates separate files for each category
- Adds reason codes to hardbounces (invalid/hardbounce, spamtrap/abuse)
- Updates master hardbounces with enhanced data

### Output Structure

All processors create:
```
your_directory/
├── valid/
│   ├── [CSN]_[SERVICE]_Valid.csv
│   └── [additional valid files]
├── invalid/
│   ├── [CSN]_[SERVICE]_Invalid.csv
│   └── [additional invalid files]
```

Where:
- `[CSN]` = Conference Segment Name
- `[SERVICE]` = SIT, MV, or EVS

### Master Hardbounces Update

All processors update the master hardbounces file at:
`H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv`

## Testing

Run the test script to verify everything is working:

```bash
python test_unified_script.py
```

## Improvements Made

### Code Organization
- **Modular design**: Separated each processor into its own module
- **Unified interface**: Single entry point with menu selection
- **Shared utilities**: Common functions for path extraction, name cleaning, etc.
- **Error handling**: Comprehensive try-catch blocks with user-friendly messages

### User Experience
- **Interactive menu**: Choose which processor to run
- **Progress bars**: Visual feedback for all operations
- **Colored output**: Status messages with appropriate colors
- **Input validation**: Checks for valid paths and required files

### Functionality Enhancements
- **Flexible column handling**: Automatically detects and handles column name variations
- **Robust file processing**: Handles missing files and columns gracefully
- **Consistent output format**: Standardized CSV output with Name and Email columns
- **Enhanced logging**: Detailed status messages and error reporting

### Maintenance Benefits
- **Single codebase**: Easier to maintain than three separate scripts
- **Consistent behavior**: All processors follow the same patterns
- **Reusable components**: Shared functions reduce code duplication
- **Easy testing**: Dedicated test script for verification

## Potential Further Improvements

1. **Configuration file**: Store paths and settings in a config file
2. **Batch processing**: Process multiple directories at once
3. **Data validation**: Verify email format and detect duplicates
4. **Reporting**: Generate summary reports of processing results
5. **GUI interface**: Create a graphical user interface
6. **Database integration**: Store results in a database instead of CSV files
7. **Scheduling**: Add ability to schedule automatic processing
8. **Email notifications**: Send completion notifications via email

## Troubleshooting

If you encounter issues:

1. Run the test script first: `python test_unified_script.py`
2. Check that all required packages are installed
3. Verify the `rich_progress.py` module is in the same directory
4. Ensure the master hardbounces path exists and is accessible
5. Check file permissions for the input and output directories

## Support

This unified script maintains all the functionality of the original three scripts while providing a better user experience and easier maintenance.
