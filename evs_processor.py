"""
EVS (ElasticEmail Verification System) Processor <PERSON><PERSON><PERSON>
Handles CSV file processing for ElasticEmail validation results.
"""

import os
import glob
import pandas as pd
import rich_progress
from email_validation_unified import log_status

def process_evs(path, csn):
    """Main EVS processing function."""
    try:
        log_status("Starting EVS (ElasticEmail Verification System) processing...", "info")
        
        # Find all CSV files in the directory
        csv_files = glob.glob('*.csv')
        if not csv_files:
            log_status("No CSV files found in the directory.", "error")
            return False
        
        log_status(f"Found {len(csv_files)} CSV files", "success")
        
        # Initialize progress bar
        total_steps = 6  # Total number of steps in the process
        progress_bar, update_progress = rich_progress.create_progress_bar(
            total=total_steps, description="Processing EVS data", color_scheme="blue"
        )
        
        # Step 1: Read and concatenate all CSV files
        log_status("Reading and concatenating CSV files...", "info")
        df_hbs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
        update_progress(1, "Read CSV files")
        
        # Step 2: Rename the 'email' column to 'Email'
        log_status("Standardizing column names...", "info")
        df_hbs.rename(columns={'email': 'Email'}, inplace=True)
        update_progress(1, "Standardized columns")
        
        # Step 3: Filter data for spam traps, invalid, high risk, low risk, and valid results
        log_status("Filtering data by validation results...", "info")
        df_st = df_hbs[df_hbs['reasons'].str.contains("PotentialSpamTrap|RoleAccount", na=False)]
        df_invalid = df_hbs[df_hbs['result'].str.contains("Invalid", na=False)]
        df_highrisk = df_hbs[df_hbs['status'].str.contains("HighRisk", na=False)]
        df_lowrisk = df_hbs[df_hbs['status'].str.contains("LowRisk", na=False)]
        df_valid = df_hbs[df_hbs['result'].str.contains("Valid", na=False)]
        
        # Print summary of filtered data
        log_status(f"Spam traps: {len(df_st)} emails", "info")
        log_status(f"Invalid: {len(df_invalid)} emails", "info")
        log_status(f"High risk: {len(df_highrisk)} emails", "info")
        log_status(f"Low risk: {len(df_lowrisk)} emails", "info")
        log_status(f"Valid: {len(df_valid)} emails", "info")
        
        update_progress(1, "Filtered data")
        
        # Step 4: Create 'invalid' and 'valid' folders if they don't exist and save filtered data there
        log_status("Saving filtered data to CSV files...", "info")
        invalid_folder = os.path.join(os.getcwd(), 'invalid')
        valid_folder = os.path.join(os.getcwd(), 'valid')
        os.makedirs(invalid_folder, exist_ok=True)
        os.makedirs(valid_folder, exist_ok=True)
        
        # Save invalid filtered data to individual CSV files with EVS suffix and conference segment name
        spam_traps_file = os.path.join(invalid_folder, f'{csn}_EVS_SpamTraps.csv')
        df_st.Email.to_csv(spam_traps_file, index=False)
        log_status(f"Saved {len(df_st)} spam traps to {spam_traps_file}", "success")
        
        invalid_file = os.path.join(invalid_folder, f'{csn}_EVS_Invalid.csv')
        df_invalid.Email.to_csv(invalid_file, index=False)
        log_status(f"Saved {len(df_invalid)} invalid emails to {invalid_file}", "success")
        
        # Save valid filtered data to individual CSV files with EVS suffix and conference segment name
        valid_file = os.path.join(valid_folder, f'{csn}_EVS_Valid.csv')
        df_valid.Email.to_csv(valid_file, index=False)
        log_status(f"Saved {len(df_valid)} valid emails to {valid_file}", "success")
        
        lowrisk_file = os.path.join(valid_folder, f'{csn}_EVS_LowRisk.csv')
        df_lowrisk.Email.to_csv(lowrisk_file, index=False)
        log_status(f"Saved {len(df_lowrisk)} low risk emails to {lowrisk_file}", "success")
        
        # Save combined valid and low-risk data
        combined_valid_file = os.path.join(valid_folder, f'{csn}_EVS_AllValid.csv')
        df_concat_valids = pd.concat([df_valid, df_lowrisk], axis=0)
        df_concat_valids.Email.to_csv(combined_valid_file, index=False)
        log_status(f"Saved {len(df_concat_valids)} combined valid emails to {combined_valid_file}", "success")
        
        update_progress(1, "Saved filtered data")
        
        # Step 5: Concatenate invalid and spam trap data with reason columns
        log_status("Updating master hardbounces file...", "info")
        
        # Add reason column to invalid emails
        df_invalid_with_reason = df_invalid[['Email']].copy()
        df_invalid_with_reason['Reason'] = 'invalid/hardbounce'
        
        # Add reason column to spam trap emails
        df_st_with_reason = df_st[['Email']].copy()
        df_st_with_reason['Reason'] = 'spamtrap/abuse'
        
        # Concatenate invalid and spam trap data with reasons
        df_concat_invalid = pd.concat([df_invalid_with_reason, df_st_with_reason], axis=0)
        log_status(f"Combined {len(df_concat_invalid)} invalid and spam trap emails with reasons", "info")
        
        master_hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
        
        try:
            df_hb_all = pd.read_csv(master_hb_path)
            log_status(f"Loaded {len(df_hb_all)} existing hardbounces", "info")
            
            # Ensure existing master file has Reason column if it doesn't exist
            if 'Reason' not in df_hb_all.columns:
                df_hb_all['Reason'] = 'legacy'  # Mark existing entries as legacy
                log_status("Added Reason column to existing master hardbounces data", "info")
            
            # Combine and remove duplicates based on Email only (keep most recent reason)
            df_concat_allinvalids = pd.concat([df_hb_all, df_concat_invalid], axis=0)
            original_count = len(df_concat_allinvalids)
            # Drop duplicates keeping the last occurrence (newest data with reasons)
            df_concat_allinvalids.drop_duplicates(subset='Email', keep='last', inplace=True)
            log_status(f"Removed {original_count - len(df_concat_allinvalids)} duplicate emails", "info")
            
            # Save back to master file with both Email and Reason columns
            df_concat_allinvalids[['Email', 'Reason']].to_csv(master_hb_path, mode='w', index=False)
            log_status(f"Updated master hardbounces file with {len(df_concat_invalid)} new entries", "success")
        
        except Exception as e:
            log_status(f"Error updating master hardbounces: {str(e)}", "error")
        
        update_progress(1, "Updated master hardbounces")
        
        # Step 6: Final processing summary
        log_status("Finalizing data processing...", "info")
        log_status(f"Valid and low-risk data already processed and saved", "success")
        update_progress(1, "Finalized processing")
        
        # Close the progress bar
        progress_bar.stop()
        
        # Print completion message
        rich_progress.print_status("\nEVS Processing Summary:", "header")
        rich_progress.print_status(f"Conference segment: {csn}", "success")
        rich_progress.print_status(f"Total records processed: {len(df_hbs)}", "success")
        rich_progress.print_status(f"Invalid records identified: {len(df_concat_invalid)}", "info")
        rich_progress.print_status(f"  - Invalid/Hardbounce: {len(df_invalid_with_reason)}", "info")
        rich_progress.print_status(f"  - Spamtrap/Abuse: {len(df_st_with_reason)}", "info")
        rich_progress.print_status(f"Valid records identified: {len(df_concat_valids)}", "info")
        rich_progress.print_status(f"  - Valid: {len(df_valid)}", "info")
        rich_progress.print_status(f"  - Low Risk: {len(df_lowrisk)}", "info")
        rich_progress.print_status(f"Files saved in 'invalid' and 'valid' folders", "success")
        
        rich_progress.print_status("\nEVS Processing Completed Successfully!", "header")
        return True
    
    except Exception as e:
        log_status(f"Error in EVS processing: {str(e)}", "error")
        try:
            progress_bar.stop()
        except:
            pass
        return False
