#!/usr/bin/env python3
"""
Test script to verify the progress bar fix works correctly.
"""

import rich_progress
import time

def test_nested_progress_bars():
    """Test that we can handle nested progress bar scenarios."""
    print("Testing nested progress bar handling...")
    
    try:
        # Create outer progress bar
        outer_bar, update_outer = rich_progress.create_progress_bar(
            total=2, description="Outer process", color_scheme="blue"
        )
        
        # Simulate first task
        update_outer(1, "First task completed")
        time.sleep(0.5)
        
        # Stop outer bar before creating inner bar
        outer_bar.stop()
        
        # Create inner progress bar
        inner_bar, update_inner = rich_progress.create_progress_bar(
            total=3, description="Inner process", color_scheme="green"
        )
        
        for i in range(3):
            update_inner(1, f"Inner task {i+1}")
            time.sleep(0.2)
        
        inner_bar.stop()
        
        print("✓ Nested progress bar test passed")
        return True
        
    except Exception as e:
        print(f"✗ Nested progress bar test failed: {e}")
        return False

def test_conditional_progress_bars():
    """Test conditional progress bar creation."""
    print("Testing conditional progress bar creation...")
    
    try:
        # Test with progress bar enabled
        use_progress = True
        if use_progress:
            bar, update = rich_progress.create_progress_bar(
                total=2, description="Conditional test", color_scheme="purple"
            )
            update(1, "Task 1")
            update(1, "Task 2")
            bar.stop()
        
        # Test without progress bar
        use_progress = False
        bar = None
        if use_progress:
            bar, update = rich_progress.create_progress_bar(
                total=2, description="Should not create", color_scheme="orange"
            )
        
        # This should not cause an error
        if use_progress and bar:
            update(1, "This should not run")
            bar.stop()
        
        print("✓ Conditional progress bar test passed")
        return True
        
    except Exception as e:
        print(f"✗ Conditional progress bar test failed: {e}")
        return False

def main():
    """Run progress bar tests."""
    print("=" * 50)
    print("PROGRESS BAR FIX VERIFICATION")
    print("=" * 50)
    
    all_passed = True
    
    if not test_nested_progress_bars():
        all_passed = False
    
    if not test_conditional_progress_bars():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ ALL PROGRESS BAR TESTS PASSED!")
        print("The progress bar conflict should be resolved.")
    else:
        print("✗ SOME PROGRESS BAR TESTS FAILED!")
    print("=" * 50)

if __name__ == "__main__":
    main()
