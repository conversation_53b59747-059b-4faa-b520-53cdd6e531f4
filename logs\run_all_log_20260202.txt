Starting master-s_2.0.py at 02-02-2026 12:27:24.97 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.11s/it]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.11s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:04<00:00,  4.36s/it]
Starting: 100%|##########| 1/1 [00:04<00:00,  4.36s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.19s/it]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.19s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.31s/it]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.31s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 11.87it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:280: DtypeWarning: Columns (36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:04<00:00,  4.10s/it]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.10s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.81it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.81it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.83s/it]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.83s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [01:08<00:00, 68.54s/it]
Finishing: 100%|##########| 1/1 [01:08<00:00, 68.54s/it]
SUCCESS: master-s_2.0.py completed successfully at 02-02-2026 12:30:08.46 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 02-02-2026 12:30:30.79 
