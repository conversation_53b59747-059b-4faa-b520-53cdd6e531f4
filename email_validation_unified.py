#!/usr/bin/env python3
"""
Unified Email Validation Processor
Combines SIT, MV, and EVS email validation processing into one script with menu selection.

Author: Unified Script
Date: 2026-01-31
"""

import os
import sys
import glob
import pandas as pd
import openpyxl
import re
import argparse
from pathlib import Path
import rich_progress
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def log_status(message, level="info"):
    """Log a message with both the logger and rich_progress status."""
    log_methods = {
        "info": logger.info,
        "warning": logger.warning,
        "error": logger.error,
        "debug": logger.debug,
        "success": logger.info
    }
    
    if level in log_methods:
        log_methods[level](message)
    else:
        logger.info(message)
    
    rich_progress.print_status(message, level)

def normalize_whitespace(text):
    """Normalize whitespace in text."""
    if not isinstance(text, str):
        return text
    normalized = re.sub(r'\s+', ' ', text)
    return normalized.strip()

def clean_name(name):
    """Clean name by removing text after first comma and normalizing whitespace."""
    if not isinstance(name, str):
        return name
    name_part = name.split(',')[0]
    return normalize_whitespace(name_part)

def extract_segment_from_path(path):
    """Extract conference segment name from path."""
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        log_status(f"Found segment: {segment}", "success")
        return segment
    else:
        log_status(f"Desired segment not found in path: {path}", "warning")
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            log_status(f"Using manually entered segment: {segment}", "info")
            return segment
        else:
            log_status("No segment name provided", "error")
            return None

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def show_menu():
    """Display the main menu and get user selection."""
    print_header("Unified Email Validation Processor")
    rich_progress.print_status("\nSelect validation service:", "info")
    rich_progress.print_status("1. SIT (Scholar Info Tech) - Excel files processing", "info")
    rich_progress.print_status("2. MV (Million Verifier) - CSV files processing", "info")
    rich_progress.print_status("3. EVS (ElasticEmail Verification System) - CSV files processing", "info")
    rich_progress.print_status("4. Exit", "warning")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return int(choice)
            else:
                rich_progress.print_status("Invalid choice. Please enter 1, 2, 3, or 4.", "error")
        except KeyboardInterrupt:
            rich_progress.print_status("\nOperation cancelled by user.", "warning")
            sys.exit(0)

def get_common_inputs():
    """Get common inputs needed by all processors."""
    print_section("Input Configuration")
    path = input("Enter location path: ").strip().strip('"\'')
    
    if not path or not os.path.exists(path):
        log_status(f"Invalid path: {path}", "error")
        return None, None
    
    # Change to the specified directory
    try:
        os.chdir(path)
        log_status(f"Changed working directory to: {path}", "info")
    except Exception as e:
        log_status(f"Failed to change directory to {path}: {str(e)}", "error")
        return None, None
    
    # Extract conference segment name
    print_section("Conference Segment Detection")
    csn = extract_segment_from_path(path)
    if not csn:
        log_status("Could not extract conference segment name", "error")
        return None, None
    
    return path, csn

def update_master_hardbounces(invalid_file_path, master_file_path):
    """Update master hardbounces file with new invalid emails."""
    update_bar = None
    try:
        if not invalid_file_path or not Path(invalid_file_path).exists():
            log_status(f"Invalid file not found: {invalid_file_path}", "warning")
            return
        
        master_path = Path(master_file_path)
        if not master_path.exists():
            log_status(f"Master hardbounces file not found: {master_file_path}", "warning")
            return
        
        log_status("Updating master hardbounces file...", "info")
        
        # Create progress bar
        update_bar, update_progress = rich_progress.create_progress_bar(
            total=3, description="Updating hardbounces", color_scheme="orange"
        )
        
        # Read files
        df_invalid = pd.read_csv(invalid_file_path)
        update_progress(1, f"Read {len(df_invalid)} invalid emails")
        
        df_hb_all = pd.read_csv(master_file_path)
        update_progress(1, f"Read {len(df_hb_all)} existing hardbounces")
        
        if 'Email' not in df_invalid.columns:
            log_status("Invalid file does not contain 'Email' column", "error")
            update_bar.stop()
            return
        
        # Ensure we only use the Email column from the invalid file
        invalid_emails = df_invalid[['Email']]
        
        # Ensure master file has Email column
        if 'Email' not in df_hb_all.columns:
            df_hb_all['Email'] = ''
            log_status("Added missing Email column to master hardbounces file", "warning")
        
        # Combine and remove duplicates
        df_concat = pd.concat([invalid_emails, df_hb_all[['Email']]], ignore_index=True)
        original_count = len(df_concat)
        df_concat.drop_duplicates(subset=['Email'], inplace=True)
        duplicates_removed = original_count - len(df_concat)
        
        # Save back to master file
        df_concat.to_csv(master_file_path, index=False, encoding='utf-8-sig')
        update_progress(1, "Saved updated hardbounces")
        
        update_bar.stop()
        
        log_status(f"Updated master hardbounces file with {len(df_invalid)} new entries", "success")
        if duplicates_removed > 0:
            log_status(f"Removed {duplicates_removed} duplicate emails", "info")
    
    except Exception as e:
        log_status(f"Error updating master hardbounces: {str(e)}", "error")
        if update_bar:
            try:
                update_bar.stop()
            except:
                pass

def main():
    """Main function."""
    try:
        while True:
            choice = show_menu()
            
            if choice == 4:
                rich_progress.print_status("Goodbye!", "success")
                break
            
            # Get common inputs
            path, csn = get_common_inputs()
            if not path or not csn:
                continue
            
            if choice == 1:
                try:
                    from sit_processor import process_sit
                    success = process_sit(path, csn)
                    if not success:
                        rich_progress.print_status("SIT processing failed", "error")
                except ImportError as e:
                    rich_progress.print_status(f"Error importing SIT processor: {e}", "error")
                except Exception as e:
                    rich_progress.print_status(f"Error in SIT processing: {e}", "error")
            elif choice == 2:
                try:
                    from mv_processor import process_mv
                    success = process_mv(path, csn)
                    if not success:
                        rich_progress.print_status("MV processing failed", "error")
                except ImportError as e:
                    rich_progress.print_status(f"Error importing MV processor: {e}", "error")
                except Exception as e:
                    rich_progress.print_status(f"Error in MV processing: {e}", "error")
            elif choice == 3:
                try:
                    from evs_processor import process_evs
                    success = process_evs(path, csn)
                    if not success:
                        rich_progress.print_status("EVS processing failed", "error")
                except ImportError as e:
                    rich_progress.print_status(f"Error importing EVS processor: {e}", "error")
                except Exception as e:
                    rich_progress.print_status(f"Error in EVS processing: {e}", "error")
            
            # Ask if user wants to continue
            print_section("Continue?")
            continue_choice = input("Do you want to process another validation service? (y/n): ").lower()
            if continue_choice not in ['y', 'yes']:
                rich_progress.print_status("Goodbye!", "success")
                break
    
    except KeyboardInterrupt:
        rich_progress.print_status("\nOperation cancelled by user (Ctrl+C).", "warning")
        sys.exit(1)
    except Exception as e:
        rich_progress.print_status(f"\nUnhandled exception: {str(e)}", "error")
        sys.exit(1)

if __name__ == "__main__":
    main()
