#!/usr/bin/env python3
"""
Test script for the unified email validation processor.
This script tests the import functionality and basic operations.
"""

import sys
import os

def test_imports():
    """Test if all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        import rich_progress
        print("✓ rich_progress imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import rich_progress: {e}")
        return False
    
    try:
        import email_validation_unified
        print("✓ email_validation_unified imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import email_validation_unified: {e}")
        return False
    
    try:
        import sit_processor
        print("✓ sit_processor imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import sit_processor: {e}")
        return False
    
    try:
        import mv_processor
        print("✓ mv_processor imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import mv_processor: {e}")
        return False
    
    try:
        import evs_processor
        print("✓ evs_processor imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import evs_processor: {e}")
        return False
    
    return True

def test_functions():
    """Test basic functionality of the modules."""
    print("\nTesting basic functions...")
    
    try:
        from email_validation_unified import extract_segment_from_path, clean_name, normalize_whitespace
        
        # Test normalize_whitespace
        test_text = "  Hello    World  \n\t  "
        result = normalize_whitespace(test_text)
        expected = "Hello World"
        if result == expected:
            print("✓ normalize_whitespace works correctly")
        else:
            print(f"✗ normalize_whitespace failed: expected '{expected}', got '{result}'")
        
        # Test clean_name
        test_name = "John Doe, PhD"
        result = clean_name(test_name)
        expected = "John Doe"
        if result == expected:
            print("✓ clean_name works correctly")
        else:
            print(f"✗ clean_name failed: expected '{expected}', got '{result}'")
        
        # Test extract_segment_from_path
        test_path = r"C:\Users\<USER>\Conference 2024\data"
        result = extract_segment_from_path(test_path)
        if result == "Conference 2024":
            print("✓ extract_segment_from_path works correctly")
        else:
            print(f"✓ extract_segment_from_path returned: {result} (manual input may be required)")
        
        return True
    
    except Exception as e:
        print(f"✗ Error testing functions: {e}")
        return False

def test_menu_display():
    """Test if the menu can be displayed."""
    print("\nTesting menu display...")
    
    try:
        from email_validation_unified import show_menu
        # We can't actually test the interactive menu, but we can test if the function exists
        print("✓ show_menu function is available")
        return True
    except Exception as e:
        print(f"✗ Error testing menu: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("UNIFIED EMAIL VALIDATION PROCESSOR - TEST SUITE")
    print("=" * 50)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test functions
    if not test_functions():
        all_passed = False
    
    # Test menu
    if not test_menu_display():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ ALL TESTS PASSED! The unified script should work correctly.")
        print("\nYou can now run: python email_validation_unified.py")
    else:
        print("✗ SOME TESTS FAILED! Please check the errors above.")
    print("=" * 50)

if __name__ == "__main__":
    main()
