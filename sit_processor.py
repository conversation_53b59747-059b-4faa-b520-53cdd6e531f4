"""
<PERSON><PERSON> (Scholar Info Tech) Processor <PERSON><PERSON><PERSON>
Handles Excel file processing for SIT validation results.
"""

import os
import pandas as pd
from pathlib import Path
import rich_progress
from email_validation_unified import log_status, clean_name, update_master_hardbounces

def rename_csv_to_xlsx(path):
    """Rename all CSV files in the directory to XLSX files."""
    rename_bar = None
    try:
        path_obj = Path(path)
        csv_files = list(path_obj.glob('*.csv'))
        
        if not csv_files:
            log_status(f"No CSV files found in {path}", "info")
            return
        
        log_status(f"Found {len(csv_files)} CSV files to rename", "info")
        
        rename_bar, update_rename = rich_progress.create_progress_bar(
            total=len(csv_files), description="Renaming CSV files", color_scheme="green"
        )
        
        for csv_file in csv_files:
            xlsx_file = csv_file.with_suffix('.xlsx')
            try:
                csv_file.rename(xlsx_file)
                update_rename(1, f"Renamed {csv_file.name} to {xlsx_file.name}")
            except Exception as e:
                log_status(f"Failed to rename {csv_file}: {str(e)}", "error")
                update_rename(1, f"Error with {csv_file.name}")
        
        rename_bar.stop()
        log_status(f"Renamed {len(csv_files)} CSV files to XLSX format", "success")
    
    except Exception as e:
        log_status(f"Error processing directory {path}: {str(e)}", "error")
        if rename_bar:
            try:
                rename_bar.stop()
            except:
                pass

def process_files(path, sheetname, csn, output_folder, output_filename=None, save_individual=True, use_progress_bar=True):
    """Process Excel files and extract data from specified sheet."""
    excel_bar = None
    try:
        path_obj = Path(path)
        output_folder_obj = Path(output_folder)
        
        # Create output directory if it doesn't exist
        output_folder_obj.mkdir(exist_ok=True, parents=True)
        log_status(f"Created output directory: {output_folder}", "info")
        
        # Find all Excel files
        files_xls = list(path_obj.glob('*.xlsx'))
        
        if not files_xls:
            log_status(f"No Excel files found in {path}", "warning")
            return None, pd.DataFrame()
        
        log_status(f"Found {len(files_xls)} Excel files to process for sheet '{sheetname}'", "info")
        
        # Initialize an empty list to store dataframes
        dfs = []
        
        # Create a progress bar for processing Excel files if requested
        if use_progress_bar:
            excel_bar, update_excel = rich_progress.create_progress_bar(
                total=len(files_xls), description=f"Processing sheet '{sheetname}'", color_scheme="purple"
            )
        
        # Process each Excel file
        for excel_file in files_xls:
            try:
                data = pd.read_excel(excel_file, sheet_name=sheetname, engine='openpyxl')
                if not data.empty:
                    dfs.append(data)
                    if use_progress_bar:
                        update_excel(1, f"Processed {excel_file.name}: {len(data)} rows")
                else:
                    if use_progress_bar:
                        update_excel(1, f"Sheet '{sheetname}' in {excel_file.name} is empty")
            except Exception as e:
                log_status(f"Error processing {excel_file}, sheet '{sheetname}': {str(e)}", "error")
                if use_progress_bar:
                    update_excel(1, f"Error with {excel_file.name}")

        if use_progress_bar and excel_bar:
            excel_bar.stop()
        
        # Combine all dataframes
        if dfs:
            df = pd.concat(dfs, ignore_index=True)
            log_status(f"Combined {len(dfs)} files for sheet '{sheetname}', total rows: {len(df)}", "success")
        else:
            log_status(f"No data found in sheet '{sheetname}' across all files", "warning")
            df = pd.DataFrame()
        
        # Rename 'Author Name' column to 'Name' if it exists
        if not df.empty and 'Author Name' in df.columns:
            df.rename(columns={'Author Name': 'Name'}, inplace=True)
            log_status(f"Renamed 'Author Name' column to 'Name' in sheet '{sheetname}'", "info")
        
        # Determine output filename
        if output_filename:
            output_file_path = output_folder_obj / f"{output_filename}.csv"
        else:
            output_file_path = output_folder_obj / f"{csn}_{sheetname}.csv"
        
        # Filter to only keep Name and Email columns if we have data
        if not df.empty and 'Email' in df.columns:
            # Check if we need to create a Name column
            if 'Name' not in df.columns:
                df['Name'] = 'Colleague'
                log_status(f"Added Name column with default value 'Colleague' to sheet '{sheetname}'", "info")
            else:
                # Clean the Name column
                df['Name'] = df['Name'].apply(clean_name)
                log_status(f"Cleaned Name column for sheet '{sheetname}'", "info")
            
            # Filter to only Name and Email columns
            df = df[['Name', 'Email']]
            log_status(f"Filtered sheet '{sheetname}' to only include Name and Email columns", "info")
        
        # Save to CSV only if save_individual is True or it's not a valid sheet
        if not df.empty and (save_individual or sheetname == 'Invalid'):
            df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            log_status(f"Saved {len(df)} rows to {output_file_path}", "success")
        elif not df.empty:
            log_status(f"Skipped saving individual file for sheet '{sheetname}' (will be included in merged file)", "info")
        else:
            log_status(f"No data to save to {output_file_path}", "warning")
        
        return str(output_file_path) if (save_individual or sheetname == 'Invalid') else None, df
    
    except Exception as e:
        log_status(f"Error in process_files: {str(e)}", "error")
        if use_progress_bar and excel_bar:
            try:
                excel_bar.stop()
            except:
                pass
        return None, pd.DataFrame()

def process_valid_sheets(path, csn, sheetnames, output_folder):
    """Process and merge valid sheets."""
    valid_dfs = []
    sheets_bar = None
    
    try:
        # Create a progress bar for processing valid sheets
        sheets_bar, update_sheets = rich_progress.create_progress_bar(
            total=len(sheetnames), description="Processing valid sheets", color_scheme="blue"
        )
        
        # Process each valid sheet but don't save individual files
        for sheetname in sheetnames:
            log_status(f"Processing valid sheet: {sheetname}", "info")
            _, df = process_files(path, sheetname, csn, output_folder, save_individual=False, use_progress_bar=False)
            if not df.empty:
                valid_dfs.append(df)
                update_sheets(1, f"Processed {sheetname}: {len(df)} rows")
            else:
                update_sheets(1, f"No data in {sheetname}")
        
        sheets_bar.stop()
        
        # Merge valid dataframes
        if valid_dfs:
            log_status("Merging valid data sheets...", "info")
            merged_df = pd.concat(valid_dfs, ignore_index=True)
            
            # Remove duplicate rows based on Email column if it exists
            if 'Email' in merged_df.columns:
                original_count = len(merged_df)
                merged_df.drop_duplicates(subset=['Email'], inplace=True)
                log_status(f"Removed {original_count - len(merged_df)} duplicate emails", "info")
                
                # Filter to only keep Name and Email columns
                if 'Name' in merged_df.columns:
                    # Clean the Name column
                    merged_df['Name'] = merged_df['Name'].apply(clean_name)
                    log_status("Cleaned Name column in merged data", "info")
                    
                    merged_df = merged_df[['Name', 'Email']]
                    log_status("Filtered output to only include Name and Email columns", "info")
                else:
                    # If Name column doesn't exist but we need it, create it with default value
                    merged_df['Name'] = 'Colleague'
                    merged_df = merged_df[['Name', 'Email']]
                    log_status("Added Name column with default value 'Colleague' and filtered output", "info")
            
            log_status(f"Merged {len(valid_dfs)} valid sheets with {len(merged_df)} total rows", "success")
            return merged_df
        else:
            log_status("No valid data to merge", "warning")
            return pd.DataFrame()
    
    except Exception as e:
        log_status(f"Error in process_valid_sheets: {str(e)}", "error")
        if sheets_bar:
            try:
                sheets_bar.stop()
            except:
                pass
        return pd.DataFrame()

def process_sit(path, csn):
    """Main SIT processing function."""
    try:
        log_status("Starting SIT (Scholar Info Tech) processing...", "info")
        
        # Step 1: Rename CSV files to XLSX
        log_status("Step 1: Renaming CSV files to XLSX", "info")
        rename_csv_to_xlsx(path)
        log_status("Step 1 completed: Renamed CSV files", "success")
        
        # Step 2: Process valid sheets and merge
        log_status("Step 2: Processing valid sheets", "info")
        valid_sheets = ['Valid+BasicCheck+DEA', 'CatchAll_AcceptAll']
        merged_valid_df = process_valid_sheets(path, csn, valid_sheets, 'valid')
        
        # Save merged valid data
        if not merged_valid_df.empty:
            valid_output = Path('valid')
            valid_output.mkdir(exist_ok=True, parents=True)
            valid_file_path = valid_output / f'{csn}_SIT_Valid.csv'
            merged_valid_df.to_csv(valid_file_path, index=False, encoding='utf-8-sig')
            log_status(f"Merged valid data saved to {valid_file_path} ({len(merged_valid_df)} rows)", "success")
        
        log_status("Step 2 completed: Processed valid sheets", "success")
        
        # Step 3: Process invalid sheet
        log_status("Step 3: Processing invalid sheet", "info")
        invalid_file_path, _ = process_files(path, 'Invalid', csn, 'invalid', f'{csn}_SIT_Invalid', use_progress_bar=True)
        log_status("Step 3 completed: Processed invalid sheet", "success")
        
        # Step 4: Update master hardbounces
        log_status("Step 4: Updating master hardbounces", "info")
        master_hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
        update_master_hardbounces(invalid_file_path, master_hb_path)
        log_status("Step 4 completed: Updated master hardbounces", "success")
        
        rich_progress.print_status("\nSIT Processing Completed Successfully!", "header")
        rich_progress.print_status(f"Conference segment: {csn}", "success")
    
    except Exception as e:
        log_status(f"Error in SIT processing: {str(e)}", "error")
        return False
    
    return True
