"""
MV (Million Verifier) Processor Module
Handles CSV file processing for Million Verifier validation results.
"""

import os
import glob
import pandas as pd
import rich_progress
from email_validation_unified import log_status, update_master_hardbounces

def process_mv(path, csn):
    """Main MV processing function."""
    try:
        log_status("Starting MV (Million Verifier) processing...", "info")
        
        # Find CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            log_status("No CSV files found in the directory.", "error")
            return False
        
        log_status(f"Found {len(csv_files)} CSV files: {', '.join(csv_files[:5])}{'...' if len(csv_files) > 5 else ''}", "info")
        
        # Initialize progress bar
        total_steps = len(csv_files) + 3  # Reading files + processing invalid + processing valid + updating master
        progress_bar, update_progress = rich_progress.create_progress_bar(
            total=total_steps, description="Processing MV files", color_scheme="green"
        )
        
        # Step 1: Read and concatenate CSV files
        try:
            df_list = []
            for f in csv_files:
                try:
                    df = pd.read_csv(f, on_bad_lines='skip')
                    df_list.append(df)
                    update_progress(1, f"Reading file {f}")
                except Exception as e:
                    log_status(f"Could not read file {f}: {str(e)}", "error")
                    update_progress(1, f"Error reading {f}")
            
            if not df_list:
                log_status("Could not read any CSV files.", "error")
                progress_bar.stop()
                return False
            
            df_hbs = pd.concat(df_list, ignore_index=True)
            log_status(f"Combined data shape: {df_hbs.shape}", "info")
            
            # Check if 'result' column exists
            if 'result' not in df_hbs.columns:
                log_status(f"'result' column not found in data. Available columns: {', '.join(df_hbs.columns)}", "error")
                progress_bar.stop()
                return False
        
        except Exception as e:
            log_status(f"Error reading CSV files: {str(e)}", "error")
            progress_bar.stop()
            return False
        
        # Step 2: Process invalid results
        try:
            invalid_options = ['unknown', 'invalid', 'disposable']
            df_invalid = df_hbs[df_hbs['result'].isin(invalid_options)]
            os.makedirs("invalid", exist_ok=True)
            
            # Check if 'Email' column exists
            if 'Email' not in df_invalid.columns:
                log_status(f"Warning: 'Email' column not found in invalid data. Available columns: {', '.join(df_invalid.columns)}", "warning")
                # Try to find an alternative column that might contain email addresses
                email_cols = [col for col in df_invalid.columns if 'email' in col.lower()]
                if email_cols:
                    log_status(f"Using '{email_cols[0]}' column instead of 'Email'", "warning")
                    df_invalid.rename(columns={email_cols[0]: 'Email'}, inplace=True)
                else:
                    log_status("No suitable email column found. Cannot proceed with invalid records.", "error")
                    update_progress(1, "Skipping invalid records")
                    progress_bar.stop()
                    return False
            
            invalid_path = f'invalid/{csn}_MV_Invalid.csv'
            df_invalid['Email'].to_csv(invalid_path, index=False)
            log_status(f"Saved invalid records to {invalid_path}", "success")
            update_progress(1, "Processed invalid records")
        
        except Exception as e:
            log_status(f"Error processing invalid results: {str(e)}", "error")
            update_progress(1, "Skipped invalid due to error")
        
        # Step 3: Process valid results
        try:
            valid_options = ['ok', 'catch_all']
            df_valid = df_hbs[df_hbs['result'].isin(valid_options)]
            os.makedirs("valid", exist_ok=True)
            
            # Check for Name column or alternatives
            if "Author Name" in df_valid.columns:
                df_valid.rename(columns={"Author Name": "Name"}, inplace=True)
            elif "Name" not in df_valid.columns:
                # Try to find an alternative column that might contain names
                name_cols = [col for col in df_valid.columns if any(name_part in col.lower() for name_part in ['name', 'author', 'person'])]
                if name_cols:
                    log_status(f"Using '{name_cols[0]}' column as 'Name'", "info")
                    df_valid.rename(columns={name_cols[0]: 'Name'}, inplace=True)
                else:
                    log_status("No suitable name column found. Adding empty 'Name' column.", "info")
                    df_valid['Name'] = 'Colleague'
            
            # Ensure Email column exists
            if 'Email' not in df_valid.columns:
                log_status(f"Warning: 'Email' column not found in valid data. Available columns: {', '.join(df_valid.columns)}", "warning")
                # Try to find an alternative column
                email_cols = [col for col in df_valid.columns if 'email' in col.lower()]
                if email_cols:
                    log_status(f"Using '{email_cols[0]}' column as 'Email'", "info")
                    df_valid.rename(columns={email_cols[0]: 'Email'}, inplace=True)
                else:
                    log_status("No suitable email column found. Cannot proceed with valid records.", "error")
                    update_progress(1, "Skipped valid processing")
                    progress_bar.stop()
                    return False
            
            valid_path = f'valid/{csn}_MV_Valid.csv'
            df_valid.to_csv(valid_path, columns=['Name', 'Email'], index=False)
            log_status(f"Saved valid records to {valid_path}", "success")
            update_progress(1, "Processed valid records")
        
        except Exception as e:
            log_status(f"Error processing valid results: {str(e)}", "error")
            update_progress(1, "Skipped valid processing due to error")
        
        # Step 4: Update master hardbounces
        try:
            master_hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
            if os.path.exists(master_hb_path):
                df_hb_all = pd.read_csv(master_hb_path)
                df_concat_allinvalids = pd.concat([df_invalid, df_hb_all], axis=0)
                # Remove duplicates
                df_concat_allinvalids = df_concat_allinvalids.drop_duplicates(subset='Email')
                df_concat_allinvalids.Email.to_csv(master_hb_path, mode='w', index=False)
                log_status(f"Updated Master_Hardbounces.csv with {len(df_invalid)} new invalid records", "success")
            else:
                log_status(f"Warning: Master hardbounces file not found at {master_hb_path}", "warning")
            
            update_progress(1, "Updated master hardbounces")
        
        except Exception as e:
            log_status(f"Error updating master hardbounces: {str(e)}", "error")
            update_progress(1, "Error updating hardbounces")
        
        # Close progress bar
        progress_bar.stop()
        
        # Print summary
        rich_progress.print_status("\nMV Processing Summary:", "header")
        rich_progress.print_status(f"Invalid records: {len(df_invalid)}", "info")
        rich_progress.print_status(f"Valid records: {len(df_valid)}", "info")
        rich_progress.print_status(f"Total records processed: {len(df_hbs)}", "info")
        rich_progress.print_status(f"Conference segment name used: {csn}", "success")
        
        rich_progress.print_status("\nMV Processing Completed Successfully!", "header")
        return True
    
    except Exception as e:
        log_status(f"Error in MV processing: {str(e)}", "error")
        try:
            progress_bar.stop()
        except:
            pass
        return False
